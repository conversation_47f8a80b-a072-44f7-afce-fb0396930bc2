import '../services/aws_auth_service.dart' as aws;

import 'linked_account_model.dart';

class UserModel {
  final String id;
  final String email;
  final String? username;
  final String?
  displayName; // Always includes provider prefix like "[xbox]Gamertag123"
  final String? phone;
  final DateTime? emailConfirmedAt;
  final DateTime? phoneConfirmedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? userMetadata;
  final Map<String, dynamic>? appMetadata;
  final List<LinkedAccountModel> linkedAccounts;

  UserModel({
    required this.id,
    required this.email,
    this.username,
    this.displayName,
    this.phone,
    this.emailConfirmedAt,
    this.phoneConfirmedAt,
    required this.createdAt,
    required this.updatedAt,
    this.userMetadata,
    this.appMetadata,
    this.linkedAccounts = const [],
  });

  /// Create UserModel from AWS User
  factory UserModel.fromAwsUser(aws.AwsUser user) {
    return UserModel(
      id: user.id,
      email: user.email,
      username: user.username,
      phone: null, // AWS Cognito phone not implemented yet
      emailConfirmedAt: user.isVerified ? DateTime.now() : null,
      phoneConfirmedAt: null,
      createdAt: DateTime.now(), // AWS user doesn't have creation date
      updatedAt: DateTime.now(),
      userMetadata: {
        'displayName': user.displayName,
        'avatarUrl': user.avatarUrl,
      },
      appMetadata: {},
      linkedAccounts: const [],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'username': username,
      'displayName': displayName,
      'phone': phone,
      'emailConfirmedAt': emailConfirmedAt?.toIso8601String(),
      'phoneConfirmedAt': phoneConfirmedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'userMetadata': userMetadata,
      'appMetadata': appMetadata,
      'linkedAccounts':
          linkedAccounts.map((account) => account.toJson()).toList(),
    };
  }

  /// Create from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    final linkedAccountsJson = json['linkedAccounts'] as List<dynamic>? ?? [];
    final linkedAccounts =
        linkedAccountsJson
            .map(
              (accountJson) => LinkedAccountModel.fromJson(
                accountJson as Map<String, dynamic>,
              ),
            )
            .toList();

    return UserModel(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      username: json['username'],
      displayName: json['displayName'],
      phone: json['phone'],
      emailConfirmedAt:
          json['emailConfirmedAt'] != null
              ? DateTime.parse(json['emailConfirmedAt'])
              : null,
      phoneConfirmedAt:
          json['phoneConfirmedAt'] != null
              ? DateTime.parse(json['phoneConfirmedAt'])
              : null,
      createdAt:
          json['createdAt'] != null
              ? DateTime.parse(json['createdAt'])
              : DateTime.now(),
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'])
              : DateTime.now(),
      userMetadata: json['userMetadata'],
      appMetadata: json['appMetadata'],
      linkedAccounts: linkedAccounts,
    );
  }

  /// Check if email is confirmed
  bool get isEmailConfirmed => emailConfirmedAt != null;

  /// Check if phone is confirmed
  bool get isPhoneConfirmed => phoneConfirmedAt != null;

  /// Get effective display name - uses custom display name if set, otherwise falls back to metadata, username, or email
  String get effectiveDisplayName {
    // Use custom display name if set (already includes provider prefix like "[xbox]Gamertag123")
    if (displayName != null && displayName!.isNotEmpty) {
      return displayName!;
    }

    // Fall back to metadata
    if (userMetadata != null) {
      final name = userMetadata!['fullName'] ?? userMetadata!['name'];
      if (name != null && name.toString().isNotEmpty) {
        return name.toString();
      }
    }
    // Use username if available before falling back to email
    if (username != null && username!.isNotEmpty) {
      return username!;
    }
    return email.split('@').first;
  }

  /// Copy with new values
  UserModel copyWith({
    String? id,
    String? email,
    String? username,
    String? displayName,
    String? phone,
    DateTime? emailConfirmedAt,
    DateTime? phoneConfirmedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? userMetadata,
    Map<String, dynamic>? appMetadata,
    List<LinkedAccountModel>? linkedAccounts,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      phone: phone ?? this.phone,
      emailConfirmedAt: emailConfirmedAt ?? this.emailConfirmedAt,
      phoneConfirmedAt: phoneConfirmedAt ?? this.phoneConfirmedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userMetadata: userMetadata ?? this.userMetadata,
      appMetadata: appMetadata ?? this.appMetadata,
      linkedAccounts: linkedAccounts ?? this.linkedAccounts,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, displayName: $displayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
